"""Comprehensive test suite for AuthService to improve coverage."""

import pytest
import uuid
from unittest.mock import AsyncMock, patch, MagicMock
from sqlalchemy.ext.asyncio import AsyncSession

from src.application.auth_service import AuthService
from src.shared.errors.exceptions import SecurityError, BusinessRuleViolation


def unique_email():
    """Generate unique email for test isolation."""
    return f"testuser_{uuid.uuid4().hex[:8]}@example.com"


class TestAuthService:
    """Test cases for AuthService class."""

    @pytest.mark.asyncio
    async def test_login_success(self, db_session: AsyncSession):
        """Test successful login with valid credentials."""
        # First register a user
        from src.application.user_service import UserService
        user_service = UserService()
        email = unique_email()
        password = "StrongPassword123!"
        
        await user_service.register_user(db_session, email, password)
        
        # Now test login
        auth_service = AuthService()
        token = await auth_service.login(db_session, email, password)
        
        assert token is not None
        assert isinstance(token, str)
        assert len(token) > 0

    @pytest.mark.asyncio
    async def test_login_invalid_email(self, db_session: AsyncSession):
        """Test login with non-existent email."""
        auth_service = AuthService()
        
        with pytest.raises(SecurityError) as exc_info:
            await auth_service.login(
                db_session, 
                "<EMAIL>", 
                "SomePassword123!"
            )
        
        assert exc_info.value.error_code == "SECURITY_INVALID_CREDENTIALS"

    @pytest.mark.asyncio
    async def test_login_invalid_password(self, db_session: AsyncSession):
        """Test login with incorrect password."""
        # First register a user
        from src.application.user_service import UserService
        user_service = UserService()
        email = unique_email()
        password = "StrongPassword123!"
        
        await user_service.register_user(db_session, email, password)
        
        # Now test login with wrong password
        auth_service = AuthService()
        
        with pytest.raises(SecurityError) as exc_info:
            await auth_service.login(db_session, email, "WrongPassword123!")
        
        assert exc_info.value.error_code == "SECURITY_INVALID_CREDENTIALS"

    @pytest.mark.asyncio
    async def test_login_inactive_user(self, db_session: AsyncSession):
        """Test login with inactive user account."""
        # First register a user
        from src.application.user_service import UserService
        user_service = UserService()
        email = unique_email()
        password = "StrongPassword123!"
        
        user = await user_service.register_user(db_session, email, password)
        
        # Deactivate the user
        user.is_active = False
        await db_session.commit()
        
        # Now test login
        auth_service = AuthService()
        
        with pytest.raises(BusinessRuleViolation) as exc_info:
            await auth_service.login(db_session, email, password)
        
        assert "account_disabled" in exc_info.value.error_code

    @pytest.mark.asyncio
    async def test_verify_token_success(self, db_session: AsyncSession):
        """Test successful token verification."""
        # First register and login to get a token
        from src.application.user_service import UserService
        user_service = UserService()
        email = unique_email()
        password = "StrongPassword123!"
        
        user = await user_service.register_user(db_session, email, password)
        
        auth_service = AuthService()
        token = await auth_service.login(db_session, email, password)
        
        # Verify the token
        verified_user = await auth_service.verify_token(db_session, token)
        
        assert verified_user is not None
        assert verified_user.id == user.id
        assert verified_user.email == email

    @pytest.mark.asyncio
    async def test_verify_token_invalid(self, db_session: AsyncSession):
        """Test token verification with invalid token."""
        auth_service = AuthService()
        
        with pytest.raises(SecurityError) as exc_info:
            await auth_service.verify_token(db_session, "invalid_token")
        
        assert exc_info.value.error_code == "SECURITY_INVALID_TOKEN"

    @pytest.mark.asyncio
    async def test_verify_token_expired(self, db_session: AsyncSession):
        """Test token verification with expired token."""
        from src.shared.security import create_access_token
        from datetime import timedelta
        
        # Create an expired token
        expired_token = create_access_token(
            {"sub": "999"}, expires_delta=timedelta(seconds=-1)
        )
        
        auth_service = AuthService()
        
        with pytest.raises(SecurityError) as exc_info:
            await auth_service.verify_token(db_session, expired_token)
        
        assert exc_info.value.error_code == "SECURITY_INVALID_TOKEN"

    @pytest.mark.asyncio
    async def test_verify_token_user_not_found(self, db_session: AsyncSession):
        """Test token verification when user no longer exists."""
        from src.shared.security import create_access_token
        
        # Create a token for a non-existent user
        token = create_access_token({"sub": "99999"})
        
        auth_service = AuthService()
        
        with pytest.raises(SecurityError) as exc_info:
            await auth_service.verify_token(db_session, token)
        
        assert exc_info.value.error_code == "SECURITY_USER_NOT_FOUND"

    @pytest.mark.asyncio
    async def test_refresh_token_success(self, db_session: AsyncSession):
        """Test successful token refresh."""
        # First register a user
        from src.application.user_service import UserService
        from src.shared.security import create_refresh_token
        user_service = UserService()
        email = unique_email()
        password = "StrongPassword123!"

        user = await user_service.register_user(db_session, email, password)

        # Create a refresh token for the user
        refresh_token = create_refresh_token({"sub": str(user.id)})

        auth_service = AuthService()

        # Refresh the token
        new_token = await auth_service.refresh_token(db_session, refresh_token)

        assert new_token is not None
        assert isinstance(new_token, str)
        assert new_token != refresh_token  # Should be different

    @pytest.mark.asyncio
    async def test_refresh_token_invalid(self, db_session: AsyncSession):
        """Test token refresh with invalid token."""
        auth_service = AuthService()
        
        with pytest.raises(SecurityError) as exc_info:
            await auth_service.refresh_token(db_session, "invalid_token")
        
        assert exc_info.value.error_code == "SECURITY_INVALID_TOKEN"

    @pytest.mark.asyncio
    async def test_logout_success(self, db_session: AsyncSession):
        """Test successful logout (token invalidation)."""
        # First register and login to get a token
        from src.application.user_service import UserService
        user_service = UserService()
        email = unique_email()
        password = "StrongPassword123!"
        
        await user_service.register_user(db_session, email, password)
        
        auth_service = AuthService()
        token = await auth_service.login(db_session, email, password)
        
        # Logout (invalidate token)
        result = await auth_service.logout(db_session, token)
        
        assert result is True

    @pytest.mark.asyncio
    async def test_logout_invalid_token(self, db_session: AsyncSession):
        """Test logout with invalid token."""
        auth_service = AuthService()
        
        with pytest.raises(SecurityError) as exc_info:
            await auth_service.logout(db_session, "invalid_token")
        
        assert exc_info.value.error_code == "SECURITY_INVALID_TOKEN"

    @pytest.mark.asyncio
    async def test_change_password_success(self, db_session: AsyncSession):
        """Test successful password change."""
        # First register a user
        from src.application.user_service import UserService
        user_service = UserService()
        email = unique_email()
        old_password = "OldPassword123!"
        new_password = "NewPassword123!"
        
        user = await user_service.register_user(db_session, email, old_password)
        
        auth_service = AuthService()
        
        # Change password
        result = await auth_service.change_password(
            db_session, user.id, old_password, new_password
        )
        
        assert result is True
        
        # Verify old password no longer works
        with pytest.raises(SecurityError):
            await auth_service.login(db_session, email, old_password)
        
        # Verify new password works
        token = await auth_service.login(db_session, email, new_password)
        assert token is not None

    @pytest.mark.asyncio
    async def test_change_password_wrong_old_password(self, db_session: AsyncSession):
        """Test password change with incorrect old password."""
        # First register a user
        from src.application.user_service import UserService
        user_service = UserService()
        email = unique_email()
        password = "StrongPassword123!"
        
        user = await user_service.register_user(db_session, email, password)
        
        auth_service = AuthService()
        
        # Try to change password with wrong old password
        with pytest.raises(SecurityError) as exc_info:
            await auth_service.change_password(
                db_session, user.id, "WrongOldPassword123!", "NewPassword123!"
            )
        
        assert exc_info.value.error_code == "SECURITY_INVALID_CREDENTIALS"

    @pytest.mark.asyncio
    async def test_reset_password_success(self, db_session: AsyncSession):
        """Test successful password reset."""
        # First register a user
        from src.application.user_service import UserService
        user_service = UserService()
        email = unique_email()
        old_password = "OldPassword123!"
        new_password = "NewPassword123!"
        
        user = await user_service.register_user(db_session, email, old_password)
        
        auth_service = AuthService()
        
        # Reset password
        result = await auth_service.reset_password(db_session, email, new_password)
        
        assert result is True
        
        # Verify old password no longer works
        with pytest.raises(SecurityError):
            await auth_service.login(db_session, email, old_password)
        
        # Verify new password works
        token = await auth_service.login(db_session, email, new_password)
        assert token is not None

    @pytest.mark.asyncio
    async def test_reset_password_user_not_found(self, db_session: AsyncSession):
        """Test password reset for non-existent user."""
        auth_service = AuthService()
        
        with pytest.raises(SecurityError) as exc_info:
            await auth_service.reset_password(
                db_session, "<EMAIL>", "NewPassword123!"
            )
        
        assert exc_info.value.error_code == "SECURITY_USER_NOT_FOUND"

    @pytest.mark.asyncio
    @patch('src.shared.observability.monitoring.track_login_attempt')
    async def test_login_tracking(self, mock_track, db_session: AsyncSession):
        """Test that login attempts are properly tracked."""
        # First register a user
        from src.application.user_service import UserService
        user_service = UserService()
        email = unique_email()
        password = "StrongPassword123!"
        
        await user_service.register_user(db_session, email, password)
        
        # Test successful login tracking
        auth_service = AuthService()
        await auth_service.login(db_session, email, password)
        
        # Verify tracking was called
        mock_track.assert_called()

    @pytest.mark.asyncio
    async def test_concurrent_logins(self, db_session: AsyncSession):
        """Test handling of concurrent login attempts."""
        # First register a user
        from src.application.user_service import UserService
        user_service = UserService()
        email = unique_email()
        password = "StrongPassword123!"
        
        await user_service.register_user(db_session, email, password)
        
        auth_service = AuthService()
        
        # Simulate concurrent logins
        import asyncio
        tasks = [
            auth_service.login(db_session, email, password)
            for _ in range(3)
        ]
        
        tokens = await asyncio.gather(*tasks)
        
        # All should succeed and return valid tokens
        for token in tokens:
            assert token is not None
            assert isinstance(token, str)

    @pytest.mark.asyncio
    @patch('src.application.auth_service.AuthService._rate_limit_check')
    async def test_rate_limiting(self, mock_rate_limit, db_session: AsyncSession):
        """Test rate limiting functionality."""
        # Mock rate limit exceeded
        mock_rate_limit.side_effect = SecurityError(
            "RATE_LIMIT_EXCEEDED",
            "Too many login attempts"
        )
        
        auth_service = AuthService()
        
        with pytest.raises(SecurityError) as exc_info:
            await auth_service.login(
                db_session, "<EMAIL>", "password"
            )
        
        assert exc_info.value.error_code == "SECURITY_RATE_LIMIT_EXCEEDED"

    @pytest.mark.asyncio
    async def test_database_error_handling(self, db_session: AsyncSession):
        """Test handling of database errors during authentication."""
        auth_service = AuthService()
        
        # Mock database session to raise an error
        with patch.object(db_session, 'execute', side_effect=Exception("DB Error")):
            with pytest.raises(Exception):  # Should propagate or handle gracefully
                await auth_service.login(
                    db_session, "<EMAIL>", "password"
                )

    @pytest.mark.asyncio
    async def test_security_event_logging(self, db_session: AsyncSession):
        """Test that security events are properly logged."""
        with patch('structlog.get_logger') as mock_logger:
            mock_log = MagicMock()
            mock_logger.return_value = mock_log
            
            auth_service = AuthService()
            
            # Test failed login logging
            with pytest.raises(SecurityError):
                await auth_service.login(
                    db_session, "<EMAIL>", "password"
                )
            
            # Verify security event was logged
            mock_log.warning.assert_called()