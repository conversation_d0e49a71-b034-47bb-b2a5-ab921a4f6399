"""Comprehensive test suite for API routes to achieve 90% coverage."""

import pytest
import uuid
from unittest.mock import AsyncMock, patch, MagicMock
from httpx import AsyncClient
from fastapi import HTT<PERSON>Exception
from sqlalchemy.ext.asyncio import AsyncSession

from src.shared.errors.exceptions import (
    BusinessRuleViolation, SecurityError, ValidationError, IntegrationError
)


def unique_email():
    """Generate unique email for test isolation."""
    return f"testuser_{uuid.uuid4().hex[:8]}@example.com"


class TestRegistrationEndpoint:
    """Test cases for user registration endpoint."""

    @pytest.mark.asyncio
    async def test_register_success(self, async_client: AsyncClient):
        """Test successful user registration."""
        email = unique_email()
        password = "StrongPassword123!"
        
        response = await async_client.post(
            "/api/v1/register",
            json={"email": email, "password": password}
        )
        
        assert response.status_code == 201
        data = response.json()
        assert data["email"] == email
        assert data["is_active"] is True
        assert "correlation_id" in data

    @pytest.mark.asyncio
    async def test_register_duplicate_email(self, async_client: AsyncClient):
        """Test registration with duplicate email."""
        email = unique_email()
        password = "StrongPassword123!"
        
        # First registration
        await async_client.post(
            "/api/v1/register",
            json={"email": email, "password": password}
        )
        
        # Second registration with same email
        response = await async_client.post(
            "/api/v1/register",
            json={"email": email, "password": "AnotherPassword123!"}
        )
        
        assert response.status_code == 400
        error_data = response.json()["error"]
        assert error_data["code"] == "EMAIL_ALREADY_EXISTS"

    @pytest.mark.asyncio
    async def test_register_invalid_email(self, async_client: AsyncClient):
        """Test registration with invalid email format."""
        response = await async_client.post(
            "/api/v1/register",
            json={"email": "invalid-email", "password": "StrongPassword123!"}
        )
        
        assert response.status_code == 422  # Validation error

    @pytest.mark.asyncio
    async def test_register_weak_password(self, async_client: AsyncClient):
        """Test registration with weak password."""
        email = unique_email()
        
        response = await async_client.post(
            "/api/v1/register",
            json={"email": email, "password": "weak"}
        )
        
        assert response.status_code == 422  # Validation error

    @pytest.mark.asyncio
    @patch('src.config.settings.get_config')
    async def test_register_when_disabled(self, mock_config, async_client: AsyncClient):
        """Test registration when registration is disabled."""
        # Mock config to disable registration
        mock_config_obj = MagicMock()
        mock_config_obj.enable_registration = False
        mock_config.return_value = mock_config_obj
        
        email = unique_email()
        response = await async_client.post(
            "/api/v1/register",
            json={"email": email, "password": "StrongPassword123!"}
        )
        
        assert response.status_code == 400
        error_data = response.json()["error"]
        assert error_data["code"] == "registration_disabled"


class TestLoginEndpoint:
    """Test cases for user login endpoint."""

    @pytest.mark.asyncio
    async def test_login_success(self, async_client: AsyncClient):
        """Test successful login."""
        email = unique_email()
        password = "StrongPassword123!"
        
        # Register user first
        await async_client.post(
            "/api/v1/register",
            json={"email": email, "password": password}
        )
        
        # Login
        response = await async_client.post(
            "/api/v1/login",
            json={"email": email, "password": password}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "access_token" in data
        assert data["token_type"] == "bearer"

    @pytest.mark.asyncio
    async def test_login_invalid_credentials(self, async_client: AsyncClient):
        """Test login with invalid credentials."""
        email = unique_email()
        password = "StrongPassword123!"
        
        # Register user first
        await async_client.post(
            "/api/v1/register",
            json={"email": email, "password": password}
        )
        
        # Login with wrong password
        response = await async_client.post(
            "/api/v1/login",
            json={"email": email, "password": "WrongPassword123!"}
        )
        
        assert response.status_code == 401
        error_data = response.json()["error"]
        assert error_data["code"] == "SECURITY_INVALID_CREDENTIALS"

    @pytest.mark.asyncio
    async def test_login_nonexistent_user(self, async_client: AsyncClient):
        """Test login with non-existent user."""
        response = await async_client.post(
            "/api/v1/login",
            json={"email": "<EMAIL>", "password": "Password123!"}
        )
        
        assert response.status_code == 401
        error_data = response.json()["error"]
        assert error_data["code"] == "SECURITY_INVALID_CREDENTIALS"


class TestProtectedEndpoints:
    """Test cases for protected endpoints requiring authentication."""

    @pytest.mark.asyncio
    async def test_me_endpoint_success(self, async_client: AsyncClient):
        """Test /me endpoint with valid token."""
        email = unique_email()
        password = "StrongPassword123!"
        
        # Register and login
        await async_client.post(
            "/api/v1/register",
            json={"email": email, "password": password}
        )
        
        login_response = await async_client.post(
            "/api/v1/login",
            json={"email": email, "password": password}
        )
        
        token = login_response.json()["access_token"]
        
        # Access /me endpoint
        response = await async_client.get(
            "/api/v1/me",
            headers={"Authorization": f"Bearer {token}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["email"] == email
        assert "correlation_id" in data

    @pytest.mark.asyncio
    async def test_me_endpoint_no_token(self, async_client: AsyncClient):
        """Test /me endpoint without token."""
        response = await async_client.get("/api/v1/me")
        
        assert response.status_code == 403  # Forbidden

    @pytest.mark.asyncio
    async def test_me_endpoint_invalid_token(self, async_client: AsyncClient):
        """Test /me endpoint with invalid token."""
        response = await async_client.get(
            "/api/v1/me",
            headers={"Authorization": "Bearer invalid_token"}
        )
        
        assert response.status_code == 401
        error_data = response.json()["error"]
        assert error_data["code"] == "SECURITY_TOKEN_INVALID"

    @pytest.mark.asyncio
    async def test_me_endpoint_expired_token(self, async_client: AsyncClient):
        """Test /me endpoint with expired token."""
        # Create an expired token
        from src.shared.security import create_access_token
        from datetime import timedelta
        
        expired_token = create_access_token(
            {"sub": "999"}, expires_delta=timedelta(seconds=-1)
        )
        
        response = await async_client.get(
            "/api/v1/me",
            headers={"Authorization": f"Bearer {expired_token}"}
        )
        
        assert response.status_code == 401
        error_data = response.json()["error"]
        assert error_data["code"] == "SECURITY_TOKEN_EXPIRED"


class TestPasswordResetEndpoints:
    """Test cases for password reset functionality."""

    @pytest.mark.asyncio
    async def test_password_reset_request_success(self, async_client: AsyncClient):
        """Test password reset request for existing user."""
        email = unique_email()
        password = "StrongPassword123!"
        
        # Register user first
        await async_client.post(
            "/api/v1/register",
            json={"email": email, "password": password}
        )
        
        # Request password reset
        with patch('src.infrastructure.email_service.send_email_async') as mock_send:
            mock_send.return_value = None
            
            response = await async_client.post(
                "/api/v1/password-reset/request",
                json={"email": email}
            )
        
        assert response.status_code == 200
        data = response.json()
        assert "message" in data

    @pytest.mark.asyncio
    async def test_password_reset_request_nonexistent_user(self, async_client: AsyncClient):
        """Test password reset request for non-existent user."""
        response = await async_client.post(
            "/api/v1/password-reset/request",
            json={"email": "<EMAIL>"}
        )
        
        # Should still return 200 for security (don't reveal if email exists)
        assert response.status_code == 200


class TestCorrelationIdHandling:
    """Test cases for correlation ID handling."""

    @pytest.mark.asyncio
    async def test_correlation_id_in_headers(self, async_client: AsyncClient):
        """Test that correlation ID is properly handled when provided in headers."""
        correlation_id = "test-correlation-123"
        
        response = await async_client.post(
            "/api/v1/register",
            json={"email": unique_email(), "password": "StrongPassword123!"},
            headers={"X-Correlation-ID": correlation_id}
        )
        
        assert response.status_code == 201
        data = response.json()
        assert data["correlation_id"] == correlation_id

    @pytest.mark.asyncio
    async def test_correlation_id_generated(self, async_client: AsyncClient):
        """Test that correlation ID is generated when not provided."""
        response = await async_client.post(
            "/api/v1/register",
            json={"email": unique_email(), "password": "StrongPassword123!"}
        )
        
        assert response.status_code == 201
        data = response.json()
        assert "correlation_id" in data
        assert data["correlation_id"].startswith("req_")


class TestErrorHandling:
    """Test cases for error handling scenarios."""

    @pytest.mark.asyncio
    @patch('src.application.user_service.UserService.register_user')
    async def test_database_error_handling(self, mock_register, async_client: AsyncClient):
        """Test handling of database errors during registration."""
        # Mock database error
        mock_register.side_effect = Exception("Database connection failed")
        
        response = await async_client.post(
            "/api/v1/register",
            json={"email": unique_email(), "password": "StrongPassword123!"}
        )
        
        assert response.status_code == 500
        error_data = response.json()["error"]
        assert "system_error" in error_data["code"]

    @pytest.mark.asyncio
    @patch('src.infrastructure.email_service.send_email_async')
    async def test_email_service_error_handling(self, mock_send, async_client: AsyncClient):
        """Test handling of email service errors."""
        email = unique_email()
        password = "StrongPassword123!"
        
        # Register user first
        await async_client.post(
            "/api/v1/register",
            json={"email": email, "password": password}
        )
        
        # Mock email service error
        mock_send.side_effect = Exception("SMTP server unavailable")
        
        response = await async_client.post(
            "/api/v1/password-reset/request",
            json={"email": email}
        )
        
        # Should handle gracefully
        assert response.status_code in [200, 500]  # Depending on implementation


class TestRateLimiting:
    """Test cases for rate limiting functionality."""

    @pytest.mark.asyncio
    async def test_registration_rate_limiting(self, async_client: AsyncClient):
        """Test rate limiting on registration endpoint."""
        # This test would require actual rate limiting implementation
        # For now, we'll test that multiple registrations work normally
        emails = [unique_email() for _ in range(3)]
        
        for email in emails:
            response = await async_client.post(
                "/api/v1/register",
                json={"email": email, "password": "StrongPassword123!"}
            )
            assert response.status_code == 201


class TestInputValidation:
    """Test cases for input validation."""

    @pytest.mark.asyncio
    async def test_empty_request_body(self, async_client: AsyncClient):
        """Test handling of empty request body."""
        response = await async_client.post("/api/v1/register", json={})
        assert response.status_code == 422  # Validation error

    @pytest.mark.asyncio
    async def test_malformed_json(self, async_client: AsyncClient):
        """Test handling of malformed JSON."""
        response = await async_client.post(
            "/api/v1/register",
            content="{invalid json}",
            headers={"Content-Type": "application/json"}
        )
        assert response.status_code == 422

    @pytest.mark.asyncio
    async def test_sql_injection_attempt(self, async_client: AsyncClient):
        """Test protection against SQL injection."""
        malicious_email = "test'; DROP TABLE users; --@example.com"
        
        response = await async_client.post(
            "/api/v1/register",
            json={"email": malicious_email, "password": "StrongPassword123!"}
        )
        
        # Should be handled by validation
        assert response.status_code == 422